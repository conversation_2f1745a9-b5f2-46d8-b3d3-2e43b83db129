const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const exifReader = require('exif-reader');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// 创建必要的目录
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');
const publicDir = path.join(__dirname, 'public');

[uploadsDir, outputDir, publicDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

// 配置multer用于文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadsDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const fileFilter = (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB限制
    }
});

// 静态文件服务
app.use(express.static('public'));
app.use('/output', express.static('output'));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 图片上传和处理路由
app.post('/upload', upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: '请选择一个图片文件' });
        }

        const inputPath = req.file.path;
        const outputFilename = 'processed-' + req.file.filename;
        const outputPath = path.join(outputDir, outputFilename);

        // 读取图片并提取EXIF信息
        const imageBuffer = fs.readFileSync(inputPath);
        const image = sharp(imageBuffer);
        const metadata = await image.metadata();

        let exifData = {};
        console.log('图片metadata:', {
            format: metadata.format,
            width: metadata.width,
            height: metadata.height,
            hasExif: !!metadata.exif,
            exifLength: metadata.exif ? metadata.exif.length : 0
        });

        if (metadata.exif) {
            try {
                // 使用exif-reader解析EXIF数据
                exifData = exifReader(metadata.exif);
                console.log('成功解析EXIF数据:', JSON.stringify(exifData, null, 2));
            } catch (error) {
                console.log('EXIF解析错误:', error.message);
                console.log('EXIF buffer前100字节:', metadata.exif.slice(0, 100));
            }
        } else {
            console.log('图片中没有EXIF数据');
        }

        // 处理图片并添加EXIF信息叠加层
        await processImageWithExif(inputPath, outputPath, exifData, metadata);

        // 清理上传的原始文件
        fs.unlinkSync(inputPath);

        res.json({
            success: true,
            exifData: formatExifData(exifData),
            outputImage: `/output/${outputFilename}`,
            originalName: req.file.originalname
        });

    } catch (error) {
        console.error('处理图片时出错:', error);
        res.status(500).json({ error: '处理图片时出错: ' + error.message });
    }
});

// 格式化EXIF数据用于显示
function formatExifData(exifData) {
    const formatted = {};

    if (!exifData) return formatted;

    // exif-reader返回的数据结构是嵌套对象
    // Image: 基本图片信息
    // Photo: 拍摄参数
    // GPSInfo: GPS信息

    // 相机信息
    if (exifData.Image?.Make) formatted['相机品牌'] = exifData.Image.Make;
    if (exifData.Image?.Model) formatted['相机型号'] = exifData.Image.Model;

    // 拍摄参数
    if (exifData.Photo?.ExposureTime) {
        const exposureTime = exifData.Photo.ExposureTime;
        if (exposureTime < 1) {
            formatted['曝光时间'] = `1/${Math.round(1/exposureTime)}s`;
        } else {
            formatted['曝光时间'] = `${exposureTime}s`;
        }
    }

    if (exifData.Photo?.FNumber) formatted['光圈'] = `f/${exifData.Photo.FNumber}`;
    if (exifData.Photo?.ISOSpeedRatings) formatted['ISO'] = `ISO ${exifData.Photo.ISOSpeedRatings}`;
    if (exifData.Photo?.FocalLength) formatted['焦距'] = `${exifData.Photo.FocalLength}mm`;
    if (exifData.Photo?.FocalLengthIn35mmFilm) formatted['35mm等效焦距'] = `${exifData.Photo.FocalLengthIn35mmFilm}mm`;

    // 拍摄时间
    if (exifData.Photo?.DateTimeOriginal) {
        const date = new Date(exifData.Photo.DateTimeOriginal);
        formatted['拍摄时间'] = date.toLocaleString('zh-CN');
    }

    // 镜头信息
    if (exifData.Photo?.LensMake) formatted['镜头品牌'] = exifData.Photo.LensMake;
    if (exifData.Photo?.LensModel) formatted['镜头型号'] = exifData.Photo.LensModel;

    // GPS信息
    if (exifData.GPSInfo) {
        const gps = exifData.GPSInfo;
        if (gps.GPSLatitude && gps.GPSLongitude && gps.GPSLatitudeRef && gps.GPSLongitudeRef) {
            const lat = gps.GPSLatitude;
            const lon = gps.GPSLongitude;

            if (Array.isArray(lat) && Array.isArray(lon)) {
                const latDeg = lat[0] + lat[1]/60 + lat[2]/3600;
                const lonDeg = lon[0] + lon[1]/60 + lon[2]/3600;
                formatted['GPS坐标'] = `${gps.GPSLatitudeRef}${latDeg.toFixed(6)}°, ${gps.GPSLongitudeRef}${lonDeg.toFixed(6)}°`;
            }
        }

        if (gps.GPSAltitude) {
            formatted['海拔高度'] = `${gps.GPSAltitude.toFixed(1)}m`;
        }
    }

    return formatted;
}

// 处理图片并添加EXIF信息白色底部区域
async function processImageWithExif(inputPath, outputPath, exifData, metadata) {
    const { width, height } = metadata;
    // 根据图片宽度自适应底部区域高度，最小150，最大200
    const bottomAreaHeight = Math.max(150, Math.min(200, Math.floor(width / 15)));
    const newHeight = height + bottomAreaHeight;

    // 创建白色底部区域
    const bottomArea = await createBottomExifArea(exifData, width, bottomAreaHeight);

    // 读取原图
    const originalImage = sharp(inputPath);

    // 创建新的画布，将原图和底部区域合并
    await sharp({
        create: {
            width: width,
            height: newHeight,
            channels: 3,
            background: { r: 255, g: 255, b: 255 }
        }
    })
    .composite([
        {
            input: await originalImage.toBuffer(),
            top: 0,
            left: 0
        },
        {
            input: bottomArea,
            top: height,
            left: 0
        }
    ])
    .jpeg({ quality: 90 })
    .toFile(outputPath);
}

// 获取品牌图标SVG
function getBrandIcon(make) {
    const brand = make?.toLowerCase() || '';

    if (brand.includes('apple') || brand.includes('iphone')) {
        // 苹果图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <path d="M-8,-10 C-8,-15 -3,-15 0,-12 C3,-15 8,-15 8,-10 C8,-5 5,-2 3,1 C1,2 0,4 0,6 C0,4 -1,2 -3,1 C-5,-2 -8,-5 -8,-10 Z"
                      fill="#666666"/>
                <circle cx="3" cy="-12" r="2" fill="#666666"/>
            </g>
        `;
    } else if (brand.includes('huawei') || brand.includes('华为')) {
        // 华为图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <text x="0" y="6" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#ff0000" text-anchor="middle">HUAWEI</text>
            </g>
        `;
    } else if (brand.includes('xiaomi') || brand.includes('小米') || brand.includes('redmi')) {
        // 小米图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <rect x="-8" y="-8" width="6" height="16" rx="3" fill="#ff6900"/>
                <rect x="-1" y="-8" width="6" height="16" rx="3" fill="#ff6900"/>
                <rect x="6" y="-4" width="6" height="12" rx="3" fill="#ff6900"/>
            </g>
        `;
    } else if (brand.includes('samsung') || brand.includes('三星')) {
        // 三星图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <text x="0" y="6" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1f4788" text-anchor="middle">SAMSUNG</text>
            </g>
        `;
    } else if (brand.includes('oppo')) {
        // OPPO图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <text x="0" y="6" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1ba784" text-anchor="middle">OPPO</text>
            </g>
        `;
    } else if (brand.includes('vivo')) {
        // vivo图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <text x="0" y="6" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#4285f4" text-anchor="middle">vivo</text>
            </g>
        `;
    } else {
        // 默认相机图标
        return `
            <g>
                <circle cx="0" cy="0" r="22" fill="#f8f8f8" stroke="#e0e0e0" stroke-width="1"/>
                <rect x="-10" y="-6" width="20" height="12" rx="2" fill="none" stroke="#666666" stroke-width="1.5"/>
                <circle cx="0" cy="0" r="4" fill="none" stroke="#666666" stroke-width="1.5"/>
                <rect x="-12" y="-8" width="6" height="4" rx="1" fill="#666666"/>
            </g>
        `;
    }
}

// 创建底部白色EXIF信息区域
async function createBottomExifArea(exifData, width, height) {
    // 增大字体大小
    const fontSize = Math.max(24, Math.floor(width / 50));
    const smallFontSize = Math.max(18, Math.floor(width / 80));
    const padding = Math.max(40, Math.floor(width / 40));

    // 准备设备信息
    let deviceText = '';
    if (exifData?.Image?.Make && exifData?.Image?.Model) {
        deviceText = `${exifData.Image.Make} ${exifData.Image.Model}`;
    }

    // 准备拍摄时间
    let dateText = '';
    if (exifData?.Photo?.DateTimeOriginal) {
        const date = new Date(exifData.Photo.DateTimeOriginal);
        dateText = date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 准备拍摄参数
    let params = [];
    if (exifData?.Photo?.FocalLength) {
        const focalLength = Math.round(exifData.Photo.FocalLength * 10) / 10;
        params.push(`${focalLength}mm`);
    }
    if (exifData?.Photo?.FNumber) {
        params.push(`f/${exifData.Photo.FNumber}`);
    }
    if (exifData?.Photo?.ExposureTime) {
        const exposureTime = exifData.Photo.ExposureTime;
        if (exposureTime < 1) {
            params.push(`1/${Math.round(1/exposureTime)}`);
        } else {
            params.push(`${exposureTime}s`);
        }
    }
    if (exifData?.Photo?.ISOSpeedRatings) {
        params.push(`ISO${exifData.Photo.ISOSpeedRatings}`);
    }

    // 使用更美观的间距连接参数
    const paramsText = params.join('  ');

    // GPS信息 - 优化格式
    let gpsText = '';
    if (exifData?.GPSInfo?.GPSLatitude && exifData?.GPSInfo?.GPSLongitude) {
        const gps = exifData.GPSInfo;
        const lat = gps.GPSLatitude;
        const lon = gps.GPSLongitude;

        if (Array.isArray(lat) && Array.isArray(lon)) {
            // 格式：23°6'6"N 113°19'7"E
            gpsText = `${Math.floor(lat[0])}°${Math.floor(lat[1])}'${Math.floor(lat[2])}"${gps.GPSLatitudeRef} ${Math.floor(lon[0])}°${Math.floor(lon[1])}'${Math.floor(lon[2])}"${gps.GPSLongitudeRef}`;
        }
    }

    // 获取品牌图标
    const brandIcon = getBrandIcon(exifData?.Image?.Make);

    // 优化文本间距和排版
    const topLineY = height/2 - 18;  // 上行文本位置
    const bottomLineY = height/2 + 18;  // 下行文本位置

    // 创建更美观的SVG布局
    const svgContent = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <!-- 白色背景 -->
            <rect width="100%" height="100%" fill="white"/>

            <!-- 顶部分割线 -->
            <line x1="0" y1="0" x2="${width}" y2="0" stroke="#e0e0e0" stroke-width="1"/>

            <!-- 左侧区域 -->
            <g>
                <!-- 设备名称 -->
                <text x="${padding}" y="${topLineY}"
                      font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                      font-size="${fontSize}"
                      font-weight="600"
                      fill="#1a1a1a"
                      letter-spacing="0.5px">
                    ${deviceText}
                </text>

                <!-- 拍摄时间 -->
                <text x="${padding}" y="${bottomLineY}"
                      font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                      font-size="${smallFontSize}"
                      font-weight="400"
                      fill="#999999"
                      letter-spacing="0.3px">
                    ${dateText}
                </text>
            </g>

            <!-- 中间品牌图标 -->
            <g transform="translate(${width/2}, ${height/2})">
                ${brandIcon}
            </g>

            <!-- 右侧区域 -->
            <g>
                <!-- 拍摄参数 -->
                <text x="${width - padding}" y="${topLineY}"
                      font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                      font-size="${fontSize}"
                      font-weight="600"
                      fill="#1a1a1a"
                      text-anchor="end"
                      letter-spacing="0.8px">
                    ${paramsText}
                </text>

                <!-- GPS坐标 -->
                <text x="${width - padding}" y="${bottomLineY}"
                      font-family="-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                      font-size="${smallFontSize}"
                      font-weight="400"
                      fill="#999999"
                      text-anchor="end"
                      letter-spacing="0.5px">
                    ${gpsText}
                </text>
            </g>
        </svg>
    `;

    return Buffer.from(svgContent);
}



// 清理output目录的路由
app.post('/cleanup', (req, res) => {
    try {
        // 读取output目录中的所有文件
        const files = fs.readdirSync(outputDir);

        // 删除所有文件
        files.forEach(file => {
            const filePath = path.join(outputDir, file);
            if (fs.statSync(filePath).isFile()) {
                fs.unlinkSync(filePath);
                console.log(`已删除文件: ${file}`);
            }
        });

        res.json({
            success: true,
            message: `已清理 ${files.length} 个文件`,
            deletedFiles: files.length
        });
    } catch (error) {
        console.error('清理文件时出错:', error);
        res.status(500).json({
            success: false,
            error: '清理文件时出错: ' + error.message
        });
    }
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});
