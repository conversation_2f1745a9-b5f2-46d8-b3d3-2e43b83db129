const express = require('express');
const multer = require('multer');
const sharp = require('sharp');
const exifReader = require('exif-reader');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// 创建必要的目录
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');
const publicDir = path.join(__dirname, 'public');

[uploadsDir, outputDir, publicDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
});

// 配置multer用于文件上传
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadsDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const fileFilter = (req, file, cb) => {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('只允许上传图片文件！'), false);
    }
};

const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB限制
    }
});

// 静态文件服务
app.use(express.static('public'));
app.use('/output', express.static('output'));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 图片上传和处理路由
app.post('/upload', upload.single('image'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: '请选择一个图片文件' });
        }

        const inputPath = req.file.path;
        const outputFilename = 'processed-' + req.file.filename;
        const outputPath = path.join(outputDir, outputFilename);

        // 读取图片并提取EXIF信息
        const imageBuffer = fs.readFileSync(inputPath);
        const image = sharp(imageBuffer);
        const metadata = await image.metadata();

        let exifData = {};
        console.log('图片metadata:', {
            format: metadata.format,
            width: metadata.width,
            height: metadata.height,
            hasExif: !!metadata.exif,
            exifLength: metadata.exif ? metadata.exif.length : 0
        });

        if (metadata.exif) {
            try {
                // 使用exif-reader解析EXIF数据
                exifData = exifReader(metadata.exif);
                console.log('成功解析EXIF数据:', JSON.stringify(exifData, null, 2));
            } catch (error) {
                console.log('EXIF解析错误:', error.message);
                console.log('EXIF buffer前100字节:', metadata.exif.slice(0, 100));
            }
        } else {
            console.log('图片中没有EXIF数据');
        }

        // 处理图片并添加EXIF信息叠加层
        await processImageWithExif(inputPath, outputPath, exifData, metadata);

        // 清理上传的原始文件
        fs.unlinkSync(inputPath);

        res.json({
            success: true,
            exifData: formatExifData(exifData),
            outputImage: `/output/${outputFilename}`,
            originalName: req.file.originalname
        });

    } catch (error) {
        console.error('处理图片时出错:', error);
        res.status(500).json({ error: '处理图片时出错: ' + error.message });
    }
});

// 格式化EXIF数据用于显示
function formatExifData(exifData) {
    const formatted = {};

    if (!exifData) return formatted;

    // exif-reader返回的数据结构是嵌套对象
    // Image: 基本图片信息
    // Photo: 拍摄参数
    // GPSInfo: GPS信息

    // 相机信息
    if (exifData.Image?.Make) formatted['相机品牌'] = exifData.Image.Make;
    if (exifData.Image?.Model) formatted['相机型号'] = exifData.Image.Model;

    // 拍摄参数
    if (exifData.Photo?.ExposureTime) {
        const exposureTime = exifData.Photo.ExposureTime;
        if (exposureTime < 1) {
            formatted['曝光时间'] = `1/${Math.round(1/exposureTime)}s`;
        } else {
            formatted['曝光时间'] = `${exposureTime}s`;
        }
    }

    if (exifData.Photo?.FNumber) formatted['光圈'] = `f/${exifData.Photo.FNumber}`;
    if (exifData.Photo?.ISOSpeedRatings) formatted['ISO'] = `ISO ${exifData.Photo.ISOSpeedRatings}`;
    if (exifData.Photo?.FocalLength) formatted['焦距'] = `${exifData.Photo.FocalLength}mm`;
    if (exifData.Photo?.FocalLengthIn35mmFilm) formatted['35mm等效焦距'] = `${exifData.Photo.FocalLengthIn35mmFilm}mm`;

    // 拍摄时间
    if (exifData.Photo?.DateTimeOriginal) {
        const date = new Date(exifData.Photo.DateTimeOriginal);
        formatted['拍摄时间'] = date.toLocaleString('zh-CN');
    }

    // 镜头信息
    if (exifData.Photo?.LensMake) formatted['镜头品牌'] = exifData.Photo.LensMake;
    if (exifData.Photo?.LensModel) formatted['镜头型号'] = exifData.Photo.LensModel;

    // GPS信息
    if (exifData.GPSInfo) {
        const gps = exifData.GPSInfo;
        if (gps.GPSLatitude && gps.GPSLongitude && gps.GPSLatitudeRef && gps.GPSLongitudeRef) {
            const lat = gps.GPSLatitude;
            const lon = gps.GPSLongitude;

            if (Array.isArray(lat) && Array.isArray(lon)) {
                const latDeg = lat[0] + lat[1]/60 + lat[2]/3600;
                const lonDeg = lon[0] + lon[1]/60 + lon[2]/3600;
                formatted['GPS坐标'] = `${gps.GPSLatitudeRef}${latDeg.toFixed(6)}°, ${gps.GPSLongitudeRef}${lonDeg.toFixed(6)}°`;
            }
        }

        if (gps.GPSAltitude) {
            formatted['海拔高度'] = `${gps.GPSAltitude.toFixed(1)}m`;
        }
    }

    return formatted;
}

// 处理图片并添加EXIF信息叠加层
async function processImageWithExif(inputPath, outputPath, exifData, metadata) {
    const image = sharp(inputPath);
    const { width, height } = metadata;
    
    // 创建EXIF信息文本
    const exifText = createExifText(exifData, metadata);
    
    // 创建文本叠加层
    const textOverlay = await createTextOverlay(exifText, width);
    
    // 合成图片
    await image
        .composite([{
            input: textOverlay,
            top: height - 120, // 距离底部120像素
            left: 20,
            blend: 'over'
        }])
        .jpeg({ quality: 90 })
        .toFile(outputPath);
}

// 创建EXIF信息文本
function createExifText(exifData, metadata) {
    let textLines = [];

    if (!exifData) return '无EXIF信息';

    // 相机信息
    if (exifData.Image?.Make && exifData.Image?.Model) {
        textLines.push(`${exifData.Image.Make} ${exifData.Image.Model}`);
    }

    // 拍摄参数
    let params = [];
    if (exifData.Photo?.FocalLength) params.push(`${exifData.Photo.FocalLength}mm`);
    if (exifData.Photo?.FNumber) params.push(`f/${exifData.Photo.FNumber}`);
    if (exifData.Photo?.ExposureTime) {
        const exposureTime = exifData.Photo.ExposureTime;
        if (exposureTime < 1) {
            params.push(`1/${Math.round(1/exposureTime)}`);
        } else {
            params.push(`${exposureTime}s`);
        }
    }
    if (exifData.Photo?.ISOSpeedRatings) params.push(`ISO${exifData.Photo.ISOSpeedRatings}`);

    if (params.length > 0) {
        textLines.push(params.join(' '));
    }

    // 拍摄时间
    if (exifData.Photo?.DateTimeOriginal) {
        const date = new Date(exifData.Photo.DateTimeOriginal);
        textLines.push(date.toLocaleString('zh-CN'));
    }

    // GPS信息
    if (exifData.GPSInfo?.GPSLatitude && exifData.GPSInfo?.GPSLongitude) {
        const gps = exifData.GPSInfo;
        const lat = gps.GPSLatitude;
        const lon = gps.GPSLongitude;

        if (Array.isArray(lat) && Array.isArray(lon)) {
            const latDeg = lat[0] + lat[1]/60 + lat[2]/3600;
            const lonDeg = lon[0] + lon[1]/60 + lon[2]/3600;
            textLines.push(`${gps.GPSLatitudeRef}${latDeg.toFixed(6)}° ${gps.GPSLongitudeRef}${lonDeg.toFixed(6)}°`);
        }
    }

    return textLines.join('\n');
}

// 创建文本叠加层
async function createTextOverlay(text, imageWidth) {
    const fontSize = Math.max(16, Math.floor(imageWidth / 50));
    const textWidth = Math.floor(imageWidth * 0.9);
    
    const svgText = `
        <svg width="${textWidth}" height="100">
            <defs>
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="1" dy="1" stdDeviation="2" flood-color="black" flood-opacity="0.8"/>
                </filter>
            </defs>
            <rect width="100%" height="100%" fill="rgba(0,0,0,0.6)" rx="5"/>
            <text x="10" y="25" font-family="Arial, sans-serif" font-size="${fontSize}" fill="white" filter="url(#shadow)">
                ${text.split('\n').map((line, index) => 
                    `<tspan x="10" dy="${index === 0 ? 0 : fontSize + 2}">${line}</tspan>`
                ).join('')}
            </text>
        </svg>
    `;
    
    return Buffer.from(svgText);
}

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
});
