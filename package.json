{"name": "exif-viewer", "version": "1.0.0", "description": "A Node.js web application for uploading images, extracting EXIF data, and generating new images with EXIF overlay", "main": "api/index.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "vercel-build": "echo 'Building for Vercel'", "build": "echo 'Build complete'"}, "keywords": ["exif", "image", "upload", "nodejs", "express"], "author": "", "license": "MIT", "dependencies": {"exif-reader": "^2.0.1", "exifr": "^7.1.3", "express": "^4.18.2", "fs": "^0.0.1-security", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "sharp": "^0.32.6"}, "devDependencies": {"nodemon": "^3.0.1"}}