document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const imageInput = document.getElementById('imageInput');
    const loadingSection = document.getElementById('loadingSection');
    const resultSection = document.getElementById('resultSection');
    const errorSection = document.getElementById('errorSection');
    const originalImage = document.getElementById('originalImage');
    const processedImage = document.getElementById('processedImage');
    const exifData = document.getElementById('exifData');
    const downloadBtn = document.getElementById('downloadBtn');
    const errorText = document.getElementById('errorText');

    // 拖拽上传功能
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // 点击上传区域触发文件选择（但不包括按钮）
    uploadArea.addEventListener('click', function(e) {
        // 如果点击的是按钮，不触发文件选择
        if (e.target.classList.contains('upload-btn')) {
            return;
        }
        imageInput.click();
    });

    // 文件选择处理
    imageInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // 处理文件选择
    function handleFileSelect(file) {
        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            showError('请选择一个有效的图片文件！');
            return;
        }

        // 验证文件大小 (10MB)
        if (file.size > 10 * 1024 * 1024) {
            showError('文件大小不能超过 10MB！');
            return;
        }

        // 显示原图预览
        const reader = new FileReader();
        reader.onload = function(e) {
            originalImage.src = e.target.result;
        };
        reader.readAsDataURL(file);

        // 上传文件
        uploadFile(file);
    }

    // 上传文件到服务器
    function uploadFile(file) {
        hideAllSections();
        loadingSection.style.display = 'block';

        const formData = new FormData();
        formData.append('image', file);

        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loadingSection.style.display = 'none';

            if (data.success) {
                displayResults(data);
            } else {
                showError(data.error || '处理图片时出现错误');
            }
        })
        .catch(error => {
            loadingSection.style.display = 'none';
            showError('网络错误：' + error.message);
        });
    }

    // 显示处理结果
    function displayResults(data) {
        // 显示处理后的图片
        processedImage.src = data.outputImage;
        
        // 显示EXIF信息
        displayExifData(data.exifData);
        
        // 设置下载链接
        downloadBtn.onclick = function() {
            downloadImage(data.outputImage, data.originalName);
        };
        
        resultSection.style.display = 'block';
    }

    // 显示EXIF数据
    function displayExifData(exifInfo) {
        exifData.innerHTML = '';
        
        if (Object.keys(exifInfo).length === 0) {
            exifData.innerHTML = '<p style="text-align: center; color: #666;">未找到EXIF信息</p>';
            return;
        }

        for (const [label, value] of Object.entries(exifInfo)) {
            const exifItem = document.createElement('div');
            exifItem.className = 'exif-item';
            
            const labelSpan = document.createElement('span');
            labelSpan.className = 'exif-label';
            labelSpan.textContent = label;
            
            const valueSpan = document.createElement('span');
            valueSpan.className = 'exif-value';
            valueSpan.textContent = value;
            
            exifItem.appendChild(labelSpan);
            exifItem.appendChild(valueSpan);
            exifData.appendChild(exifItem);
        }
    }

    // 下载图片
    function downloadImage(imagePath, originalName) {
        const link = document.createElement('a');
        link.href = imagePath;
        
        // 生成下载文件名
        const extension = originalName.split('.').pop();
        const baseName = originalName.replace(`.${extension}`, '');
        link.download = `${baseName}_with_exif.${extension}`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 显示错误信息
    function showError(message) {
        hideAllSections();
        errorText.textContent = message;
        errorSection.style.display = 'block';
        
        // 3秒后自动隐藏错误信息
        setTimeout(() => {
            errorSection.style.display = 'none';
        }, 3000);
    }

    // 隐藏所有结果区域
    function hideAllSections() {
        loadingSection.style.display = 'none';
        resultSection.style.display = 'none';
        errorSection.style.display = 'none';
    }

    // 重置上传区域
    function resetUpload() {
        imageInput.value = '';
        hideAllSections();
    }

    // 添加重新上传功能
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            resetUpload();
        }
    });
});
